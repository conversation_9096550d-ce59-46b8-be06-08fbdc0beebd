# 层级导航功能修改总结

## 修改目标
实现在滚轮滚动时，能够从深层子控件逐级向上找到父控件，直到根窗口，以及向下找到子控件的功能。

## 修改的文件

### 1. OCRTools/WindowInfo.cs
**新增属性：**
- `CachedLevel`: 缓存层级深度，避免重复计算
- `ParentCtrlHandle`: 存储真实的直接父控件句柄（不改变现有的 ParentHandle）

**新增方法：**
- `GetLevel()`: 获取控件在层级树中的深度
- `GetDirectParent()`: 获取直接父控件
- `GetDirectChildren()`: 获取直接子控件列表

### 2. Common/CommonAutomation.cs
**修改 InitChildHandle 方法：**
- 添加了 `BuildParentChildMap()` 方法调用，构建父子关系映射表
- 修改 WindowInfo 创建逻辑，设置 `ParentCtrlHandle` 属性

**新增 BuildParentChildMap 方法：**
- 基于几何包含关系构建父子映射
- 选择最小包含矩形作为直接父级
- 避免额外的 API 调用，性能最优

### 3. OCRTools/SelectRectangleList.cs
**修改 EvalWindow 方法：**
- 为根窗口设置 `ParentCtrlHandle = IntPtr.Zero`
- 为客户区设置 `ParentCtrlHandle = handle`

**修改 EvalControl 方法：**
- 使用 `NativeMethods.GetParent()` 获取真实的直接父级
- 设置 `ParentCtrlHandle` 为真实的父控件句柄
- 递归时传递当前控件句柄而不是原始窗口句柄

### 4. ShareX/Shapes/ShapeManager.cs
**重写 FindSelectedWindow 方法：**
- 优先选择层级更深的控件
- 支持滚轮层级导航

**新增层级导航方法：**
- `HandleHierarchicalNavigation()`: 处理层级导航逻辑
- `NavigateToParent()`: 向父级导航
- `NavigateToChild()`: 向子级导航
- `FindDeepestChildAtMouse()`: 递归查找最深层子控件
- `NavigateInSameLevel()`: 同层级循环切换

## 功能特性

### 滚轮导航行为
- **向上滚动**: 当前控件 → 直接父控件 → 父控件的父控件 → ... → 根窗口
- **向下滚动**: 当前控件 → 直接子控件 → 最深层子控件
- **边界处理**: 到达根窗口或最深子控件时，在同层级中循环切换

### 优先级处理
1. **前台窗口** > **鼠标下方窗口** > **Z-Order高的窗口** > **距离近的窗口**
2. **层级深度**: 优先选择层级更深的控件
3. **面积大小**: 同层级中优先选择面积较小的控件

### 性能优化
1. **缓存层级深度**: 避免重复计算
2. **几何关系映射**: 基于已有的 BoundingRectangle 信息，无需额外 API 调用
3. **批量处理**: 一次性构建完整的父子关系映射
4. **智能过滤**: 只处理包含鼠标位置的控件

## 兼容性保证
- 保持现有 `ParentHandle` 属性不变，确保现有代码不受影响
- 新增的功能通过新属性 `ParentCtrlHandle` 实现
- 所有现有的窗口检测逻辑保持不变

## 支持的控件获取方式
1. **SelectRectangleList + EnumChildWindows**: Win32 API 方式
2. **CommonAutomation + TreeScope.Children**: UI Automation 直接子控件
3. **CommonAutomation + TreeScope.Descendants**: UI Automation 所有后代控件

## 测试建议
1. 测试不同深度的控件层级导航
2. 测试在复杂窗口结构中的滚轮切换
3. 验证性能表现，特别是在控件数量较多的窗口中
4. 确认现有功能不受影响

## 修复的问题

### 问题1：SelectRectangleList 中 EvalControl 的逻辑错误
**原问题：**
- `EnumChildWindows` 参数传递错误，传递了 `handle` 而不是 `lParam`
- `realParent` 逻辑缺少验证，可能指向无效的父级

**修复方案：**
- 恢复 `EnumChildWindows(handle, lpEnumFunc, lParam)` 的原始参数传递
- 增强 `realParent` 验证：如果 `GetParent` 返回无效值，则使用 `lParam`
- 确保 `ParentHandle` 始终指向根窗口，保持现有逻辑不变

### 问题2：FindSelectedWindow 逻辑过于复杂
**原问题：**
- 单个方法承担过多职责，难以理解和维护
- 层级导航逻辑混杂在一起，不易调试

**修复方案：**
- 拆分成多个职责单一的方法：
  - `GetCurrentMousePosition()`: 获取鼠标位置
  - `ShouldResetSelection()`: 判断是否需要重置选择
  - `IsMouseOutOfBounds()`: 检查鼠标边界
  - `GetCandidatesAtMouse()`: 获取候选控件
  - `IsInitialSelection()`: 判断是否为初始选择
  - `ProcessScrollNavigation()`: 处理滚轮导航
  - `FindParentWindow()`: 查找父级窗口
  - `FindChildWindow()`: 查找子级窗口
  - `NavigateInSameLevel()`: 同层级导航

## 注意事项
- 缓存失效的情况不做考虑，因为截图是即时性操作
- 层级计算使用递归，对于极深的控件树可能有性能影响
- UI Automation API 可能抛出异常，已添加异常处理
- 修复后的代码更易于理解、调试和维护

## 修复验证
- 所有修改的文件都通过了语法检查
- 保持了现有代码的兼容性
- 层级导航逻辑更加清晰和可靠
