using OCRTools.Common;
using System;
using System.Drawing;
using System.Linq;

namespace OCRTools
{
    public class WindowInfo
    {
        private string _className;

        private Icon _icon; // public Icon Get  NativeMethods.GetApplicationIcon(Handle);

        private string _text;

        public WindowInfo(IntPtr handle)
        {
            Handle = handle;
        }

        public WindowInfo(IntPtr handle, Rectangle rect)
        {
            Handle = handle;
            Rectangle = rect;
        }

        public WindowInfo()
        {
        }

        public bool IsSmallControl { get; set; }

        public IntPtr ParentHandle { get; set; }

        public IntPtr Handle { get; set; }

        public Rectangle Rectangle { get; set; }

        public bool IsWindow { get; set; }

        public bool IsHandleCreated => Handle != IntPtr.Zero;

        public bool IsParentHandleCreated => ParentHandle != IntPtr.Zero;

        public int ZIndex { get; internal set; }

        // 新增属性
        public int? CachedLevel { get; set; } = null;           // 缓存层级深度
        public IntPtr ParentCtrlHandle { get; set; }            // 真实的直接父控件句柄

        public Rectangle GetRectangle()
        {
            if (Rectangle.IsEmpty) Rectangle = Handle.GetRectangle(IsWindow);
            return Rectangle;
        }

        // 新增辅助方法
        public int GetLevel(System.Collections.Concurrent.ConcurrentBag<WindowInfo> allWindows)
        {
            if (CachedLevel.HasValue) return CachedLevel.Value;

            if (IsWindow && ParentCtrlHandle == IntPtr.Zero)
            {
                CachedLevel = 0; // 根窗口
                return 0;
            }

            var parent = allWindows.FirstOrDefault(w => w.Handle == ParentCtrlHandle);
            if (parent != null)
            {
                CachedLevel = parent.GetLevel(allWindows) + 1;
            }
            else
            {
                CachedLevel = IsWindow ? 0 : 1; // 找不到父级时的默认值
            }

            return CachedLevel.Value;
        }

        public WindowInfo GetDirectParent(System.Collections.Concurrent.ConcurrentBag<WindowInfo> allWindows)
        {
            if (ParentCtrlHandle == IntPtr.Zero) return null;
            return allWindows.FirstOrDefault(w => w.Handle == ParentCtrlHandle);
        }

        public System.Collections.Generic.List<WindowInfo> GetDirectChildren(System.Collections.Concurrent.ConcurrentBag<WindowInfo> allWindows)
        {
            return allWindows.Where(w => w.ParentCtrlHandle == Handle).ToList();
        }

        public bool Activate()
        {
            bool result;
            if (IsParentHandleCreated)
            {
                NativeMethods.SetActiveWindow(ParentHandle);
                result = NativeMethods.SetForegroundWindow(ParentHandle);
                //NativeMethods.SetFocus(ParentHandle);
            }
            else
            {
                NativeMethods.SetActiveWindow(Handle);
                result = NativeMethods.SetForegroundWindow(Handle);
                //NativeMethods.SetFocus(Handle);
            }
            return result;
        }

        public string GetClassName()
        {
            if (string.IsNullOrEmpty(_className)) _className = NativeMethods.GetClassName(Handle);
            return _className;
        }

        public string GetText()
        {
            if (string.IsNullOrEmpty(_text)) _text = NativeMethods.GetWindowText(Handle);
            return _text;
        }

        public Icon GetIcon()
        {
            if (_icon != null) _icon = NativeMethods.GetApplicationIcon(Handle);
            return _icon;
        }

        public bool GetIsMinimized()
        {
            return NativeMethods.IsIconic(Handle);
        }

        public void Restore()
        {
            if (IsHandleCreated) NativeMethods.ShowWindow(Handle, (int)WindowShowStyle.Restore);
        }

        //internal void Scal()
        //{
        //    if (PrimaryScreen.ScreenScalingFactor < 1)
        //    {
        //        this.Rectangle = new Rectangle(
        //            new Point((int)(Rectangle.Location.X / PrimaryScreen.ScreenScalingFactor)
        //            , (int)(Rectangle.Location.Y / PrimaryScreen.ScreenScalingFactor))
        //            , new Size((int)(Rectangle.Size.Width / PrimaryScreen.ScreenScalingFactor)
        //            , (int)(Rectangle.Size.Height / PrimaryScreen.ScreenScalingFactor))
        //            );
        //    }
        //}

        internal void OffSet(Rectangle rectangle, Point offSet)
        {
            Rectangle = Rectangle.Intersect(Rectangle, rectangle);
            if (!Rectangle.Location.IsEmpty && !Rectangle.Size.IsEmpty && Rectangle.IsValid())
            {
                Rectangle = new Rectangle(new Point(Rectangle.Location.X + offSet.X, Rectangle.Location.Y + offSet.Y), Rectangle.Size);
            }
        }
    }
}