using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Automation;

namespace OCRTools.Common
{
    public class CommonAutomation
    {
        public static List<WindowInfo> InitChildHandle(WindowInfo window, TreeScope scope)
        {
            var lstWindows = new List<WindowInfo>();

            if (!StaticValue.IsCatchScreen)
                return lstWindows;

            try
            {
                var existingRectangles = new HashSet<Rectangle>();

                // 获取UI自动化元素
                var element = AutomationElement.FromHandle(window.Handle);
                if (element == null)
                {
                    return lstWindows;
                }

                // 查找子元素 - 使用控件视图条件，避免中间层级控件被过滤导致断层
                var condition = Automation.ControlViewCondition;
                var foundElements = element.FindAll(scope, condition);

                if (foundElements == null || !StaticValue.IsCatchScreen)
                {
                    return lstWindows;
                }

                if (foundElements.Count > 0)
                {
                    lstWindows.Capacity = foundElements.Count;
                }

                foreach (AutomationElement item in foundElements)
                {
                    if (!StaticValue.IsCatchScreen)
                        break;

                    try
                    {
                        var boundingRect = item.Current.BoundingRectangle;

                        if (boundingRect.IsEmpty || boundingRect.Width <= 0 || boundingRect.Height <= 0)
                            continue;

                        var rect = new Rectangle(
                            (int)boundingRect.X,
                            (int)boundingRect.Y,
                            (int)boundingRect.Width,
                            (int)boundingRect.Height);

                        if (rect.IsValid())
                        {
                            var nativeHandle = item.Current.NativeWindowHandle;

                            // 检查是否已经存在相同Rectangle的控件（恢复老版本的简单逻辑）
                            bool isDuplicate = existingRectangles.Contains(rect);

                            if (!isDuplicate)
                            {
                                lstWindows.Add(new WindowInfo
                                {
                                    IsSmallControl = true,
                                    ParentHandle = window.Handle,
                                    Handle = nativeHandle != 0 ? new IntPtr(nativeHandle) : IntPtr.Zero,
                                    Rectangle = rect,
                                    ZIndex = window.ZIndex
                                });

                                // 添加到去重集合
                                existingRectangles.Add(rect);
                            }
                        }
                    }
                    catch (ElementNotAvailableException)
                    {
                        continue;
                    }
                    catch (InvalidOperationException)
                    {
                        continue;
                    }
                }
            }
            catch (ElementNotAvailableException)
            {
            }
            catch (Exception ex)
            {
            }

            return lstWindows;
        }
    }
}
