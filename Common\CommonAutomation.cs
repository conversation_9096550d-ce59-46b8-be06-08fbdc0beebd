﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Automation;

namespace OCRTools.Common
{
    public class CommonAutomation
    {
        public static List<WindowInfo> InitChildHandle(WindowInfo window, TreeScope scope)
        {
            var lstWindows = new List<WindowInfo>();

            if (!StaticValue.IsCatchScreen)
                return lstWindows;

            try
            {
                var existingRectangles = new HashSet<Rectangle>();

                // 获取UI自动化元素
                var element = AutomationElement.FromHandle(window.Handle);
                if (element == null)
                {
                    return lstWindows;
                }

                // 查找子元素
                var condition = new PropertyCondition(AutomationElement.IsOffscreenProperty, false);
                var foundElements = element.FindAll(scope, condition);

                if (foundElements == null || !StaticValue.IsCatchScreen)
                {
                    return lstWindows;
                }

                if (foundElements.Count > 0)
                {
                    lstWindows.Capacity = foundElements.Count;
                }

                // 🔥 关键优化：构建父子关系映射表
                var parentChildMap = BuildParentChildMap(foundElements, window.Handle);

                foreach (AutomationElement item in foundElements)
                {
                    if (!StaticValue.IsCatchScreen)
                        break;

                    try
                    {
                        var boundingRect = item.Current.BoundingRectangle;

                        if (boundingRect.IsEmpty || boundingRect.Width <= 0 || boundingRect.Height <= 0)
                            continue;

                        var rect = new Rectangle(
                            (int)boundingRect.X,
                            (int)boundingRect.Y,
                            (int)boundingRect.Width,
                            (int)boundingRect.Height);

                        if (rect.IsValid())
                        {
                            var nativeHandle = item.Current.NativeWindowHandle;
                            var currentHandle = nativeHandle != 0 ? new IntPtr(nativeHandle) : IntPtr.Zero;

                            // 🔥 从映射表中获取父控件句柄，无需额外 API 调用
                            IntPtr realParentHandle = parentChildMap.ContainsKey(currentHandle)
                                ? parentChildMap[currentHandle]
                                : window.Handle;

                            // 检查是否已经存在相同Rectangle的控件
                            bool isDuplicate = existingRectangles.Contains(rect);

                            if (!isDuplicate)
                            {
                                lstWindows.Add(new WindowInfo
                                {
                                    IsSmallControl = true,
                                    ParentHandle = window.Handle,        // 保持现有逻辑不变
                                    ParentCtrlHandle = realParentHandle, // 🔥 新属性：真实的父控件句柄
                                    Handle = currentHandle,
                                    Rectangle = rect,
                                    ZIndex = window.ZIndex
                                });

                                // 添加到去重集合
                                existingRectangles.Add(rect);
                            }
                        }
                    }
                    catch (ElementNotAvailableException)
                    {
                        continue;
                    }
                    catch (InvalidOperationException)
                    {
                        continue;
                    }
                }
            }
            catch (ElementNotAvailableException)
            {
            }
            catch (Exception ex)
            {
            }

            return lstWindows;
        }

        // 🔥 核心方法：基于几何包含关系构建父子映射
        private static Dictionary<IntPtr, IntPtr> BuildParentChildMap(AutomationElementCollection elements, IntPtr windowHandle)
        {
            var parentChildMap = new Dictionary<IntPtr, IntPtr>();
            var elementInfos = new List<(IntPtr Handle, Rectangle Rect)>();

            // 1. 收集所有元素的句柄和矩形信息
            foreach (AutomationElement element in elements)
            {
                try
                {
                    var nativeHandle = element.Current.NativeWindowHandle;
                    if (nativeHandle != 0)
                    {
                        var handle = new IntPtr(nativeHandle);
                        var boundingRect = element.Current.BoundingRectangle;
                        var rect = new Rectangle(
                            (int)boundingRect.X,
                            (int)boundingRect.Y,
                            (int)boundingRect.Width,
                            (int)boundingRect.Height);

                        if (rect.IsValid())
                        {
                            elementInfos.Add((handle, rect));
                        }
                    }
                }
                catch
                {
                    continue;
                }
            }

            // 2. 基于几何包含关系构建父子映射
            foreach (var (childHandle, childRect) in elementInfos)
            {
                IntPtr bestParent = windowHandle;
                int smallestParentArea = int.MaxValue;

                // 找到包含当前元素的最小父元素
                foreach (var (parentHandle, parentRect) in elementInfos)
                {
                    if (parentHandle != childHandle &&
                        parentRect.Contains(childRect) &&
                        parentRect.Width * parentRect.Height < smallestParentArea)
                    {
                        bestParent = parentHandle;
                        smallestParentArea = parentRect.Width * parentRect.Height;
                    }
                }

                parentChildMap[childHandle] = bestParent;
            }

            return parentChildMap;
        }
    }
}
