using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace OCRTools
{
    public class SelectRectangleList
    {
        public IntPtr IgnoreHandle { get; set; }
        public bool IncludeChildWindows { get; set; }

        private List<WindowInfo> windows;
        private HashSet<IntPtr> parentHandles;

        public void SetWindowZOrder(ConcurrentBag<WindowInfo> lstTmp)
        {
            if (!lstTmp.Any(p => p.IsWindow)) return;

            // 优化1：先处理窗口Z-Order，建立父窗口映射
            var windowList = lstTmp.Where(p => p.IsWindow).ToList();
            var parentMap = new ConcurrentDictionary<IntPtr, int>();

            // 并行计算窗口Z-Order
            Parallel.ForEach(windowList, new ParallelOptions { MaxDegreeOfParallelism = Math.Max(5, Environment.ProcessorCount) },
                window =>
                {
                    window.ZIndex = GetWindowZOrder(window.Handle);
                    parentMap.TryAdd(window.Handle, window.ZIndex);
                });

            // 优化2：使用映射表快速设置子控件Z-Order，保持原始逻辑
            var childControls = lstTmp.Where(p => !p.IsWindow).ToList();
            Parallel.ForEach(childControls, new ParallelOptions { MaxDegreeOfParallelism = Math.Max(5, Environment.ProcessorCount) },
                child =>
                {
                    // 保持原始的双重检查逻辑
                    if (parentMap.TryGetValue(child.ParentHandle, out int parentZIndex))
                    {
                        child.ZIndex = parentZIndex;
                    }
                    else
                    {
                        // 回退到原始查找逻辑，处理复杂的父子关系
                        var parent = lstTmp.FirstOrDefault(q => q.IsWindow &&
                            (Equals(q.Handle, child.ParentHandle) || Equals(q.ParentHandle, child.ParentHandle)));
                        if (parent != null)
                            child.ZIndex = parent.ZIndex;
                    }
                });
        }

        private int GetWindowZOrder(IntPtr hWnd)
        {
            var zOrder = -1;
            while ((hWnd = NativeMethods.GetWindow(hWnd, 2 /* GW_HWNDNEXT */)) != IntPtr.Zero) zOrder++;
            return zOrder;
        }

        public ConcurrentBag<WindowInfo> GetWindowInfoListAsync(int timeout, IntPtr parent)
        {
            windows = new List<WindowInfo>();
            parentHandles = new HashSet<IntPtr>();

            try
            {
                // 使用Task替代Thread，支持取消令牌
                using (var cts = new CancellationTokenSource(timeout))
                {
                    var task = Task.Run(() =>
                    {
                        NativeMethods.EnumWindowsProc ewp = EvalWindow;
                        NativeMethods.EnumWindows(ewp, parent);
                    }, cts.Token);

                    try
                    {
                        task.Wait(cts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        // 超时取消，正常处理
                    }
                }
            }
            catch { }

            // 直接返回ConcurrentBag，避免双重存储
            return new ConcurrentBag<WindowInfo>(windows);
        }

        private void AddWindowInfo(List<WindowInfo> lstT, WindowInfo wd)
        {
            if (wd.Rectangle.IsValid())
            {
                //if (PrimaryScreen.ScreenScalingFactor > 1)
                //{
                //    wd.Rectangle = PrimaryScreen.GetDisplayRectangle(wd.Rectangle);
                //    if (!wd.Rectangle.IsValid())
                //    {
                //        return;
                //    }
                //}
                if (!lstT.Any(q => q.Rectangle.Equals(wd.Rectangle)))
                {
                    if (wd.IsWindow && wd.Rectangle.Y + wd.Rectangle.Height < 10)
                    {
                        return;
                    }
                    lstT.Add(wd);
                }
            }
        }

        private bool EvalWindow(IntPtr handle, IntPtr lParam)
        {
            if (handle == IgnoreHandle || !NativeMethods.IsWindowVisible(handle) || NativeMethods.IsWindowCloaked(handle))
            {
                return true;
            }

            var windowInfo = new WindowInfo(handle)
            {
                IsWindow = true,
                ParentCtrlHandle = IntPtr.Zero  // 🔥 根窗口没有父控件
            };

            if (!windowInfo.GetRectangle().IsValid())
            {
                return true;
            }

            AddWindowInfo(windows, windowInfo);

            if (IncludeChildWindows && parentHandles.Add(handle))
            {
                NativeMethods.EnumWindowsProc ewp = EvalControl;
                NativeMethods.EnumChildWindows(handle, ewp, handle);
            }

            var clientRect = NativeMethods.GetClientRect(handle);

            if (clientRect.IsValid() && clientRect != windowInfo.Rectangle)
            {
                var newWindow = new WindowInfo(handle, clientRect)
                {
                    ParentHandle = handle,      // 保持现有逻辑
                    ParentCtrlHandle = handle,  // 🔥 客户区的父控件是窗口本身
                    IsWindow = true
                };
                AddWindowInfo(windows, newWindow);
            }

            return true;
        }

        private bool EvalControl(IntPtr handle, IntPtr lParam)
        {
            if (!NativeMethods.IsWindowVisible(handle)) return true;

            // 获取真实的直接父窗口句柄
            IntPtr realParent = NativeMethods.GetParent(handle);
            // 如果 GetParent 返回 IntPtr.Zero 或者返回的父级不是当前枚举范围内的窗口，
            // 则使用 lParam（原始窗口句柄）作为父级
            if (realParent == IntPtr.Zero || realParent == handle)
            {
                realParent = lParam;
            }

            var windowInfo = new WindowInfo(handle)
            {
                IsWindow = false,
                ParentHandle = lParam,        // 保持现有逻辑不变：始终指向根窗口
                ParentCtrlHandle = realParent // 真实的直接父级，但确保在合理范围内
            };

            if (!windowInfo.GetRectangle().IsValid()) return true;

            // 🔥 关键修复：恢复原始参数传递
            if (parentHandles.Add(handle))
            {
                NativeMethods.EnumWindowsProc lpEnumFunc = EvalControl;
                NativeMethods.EnumChildWindows(handle, lpEnumFunc, lParam); // 传递 lParam，不是 handle
            }

            AddWindowInfo(windows, windowInfo);
            return true;
        }
    }
}