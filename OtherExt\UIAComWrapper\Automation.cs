using System.Runtime.InteropServices;
using UIAutomationClient;

namespace System.Windows.Automation
{
    public static class Automation
    {
        private static IUIAutomation _factory;

        // 预定义的视图条件
        public static readonly Condition ContentViewCondition = Condition.Wrap(Factory.ContentViewCondition);
        public static readonly Condition ControlViewCondition = Condition.Wrap(Factory.ControlViewCondition);
        public static readonly Condition RawViewCondition = Condition.Wrap(Factory.RawViewCondition);

        // Explicit static constructor to tell C# compiler
        // not to mark type as beforefieldinit
        static Automation()
        {
        }

        public static IUIAutomation Factory
        {
            get
            {
                // Try CUIAutomation8
                if (_factory == null)
                    try
                    {
                        _factory = new CUIAutomation8Class();
                    }
                    catch (COMException)
                    {
                    }

                // Fall back to CUIAutomation
                return _factory ?? (_factory = new CUIAutomationClass());
            }
        }
    }
}