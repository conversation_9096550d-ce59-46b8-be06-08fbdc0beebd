﻿using OCRTools.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace OCRTools.ScreenCaptureLib
{

    internal partial class ShapeManager : IDisposable
    {
        public List<BaseShape> Shapes { get; private set; } = new List<BaseShape>();

        private BaseShape currentShape;

        public BaseShape CurrentShape
        {
            get
            {
                return currentShape;
            }
            private set
            {
                if (Equals(currentShape, value)) return;
                currentShape = value;

                if (currentShape != null)
                {
                    currentShape.OnConfigSave();
                }

                OnCurrentShapeChanged(currentShape);
            }
        }

        private ShapeType currentTool = ShapeType.矩形;

        public ShapeType CurrentTool
        {
            get
            {
                return currentTool;
            }
            set
            {
                if (Equals(currentTool, value)) return;

                currentTool = value;

                if (Form.IsAnnotationMode)
                {
                    if (IsCurrentShapeTypeRegion)
                    {
                        Options.LastRegionTool = CurrentTool;
                    }
                    else if (Form.IsEditorMode)
                    {
                        Options.LastEditorTool = CurrentTool;
                    }
                    else
                    {
                        Options.LastAnnotationTool = CurrentTool;
                    }

                    ClearTools();
                }

                if (CurrentShape != null)
                {
                    // do not keep selection if select tool does not handle it
                    if (currentTool == ShapeType.选择并移动)
                    {
                        if (!CurrentShape.IsHandledBySelectTool)
                        {
                            DeselectCurrentShape();
                        }
                    }
                    // do not keep selection if we switch away from a tool and the selected shape does not match the new type
                    else if (CurrentShape.ShapeType != currentTool)
                    {
                        DeselectCurrentShape();
                    }
                }

                OnCurrentShapeTypeChanged(currentTool);
            }
        }

        public ShapeType CurrentShapeTool
        {
            get
            {
                ShapeType tool = CurrentTool;

                if (tool == ShapeType.选择并移动)
                {
                    BaseShape shape = CurrentShape;

                    if (shape != null)
                    {
                        tool = shape.ShapeType;
                    }
                }

                return tool;
            }
        }

        public Rectangle CurrentRectangle
        {
            get
            {
                if (CurrentShape != null)
                {
                    return CurrentShape.Rectangle;
                }

                return Rectangle.Empty;
            }
        }

        public bool IsSmallControlModel { get; set; }

        public bool IsCurrentShapeValid => CurrentShape != null && CurrentShape.IsValidShape;

        public BaseRegionShape[] Regions => Shapes.OfType<BaseRegionShape>().ToArray();

        public BaseShape[] ValidRegions => Regions.Where(x => x.IsValidShape).ToArray();

        public BaseDrawingShape[] DrawingShapes => Shapes.OfType<BaseDrawingShape>().ToArray();

        public BaseEffectShape[] EffectShapes => Shapes.OfType<BaseEffectShape>().ToArray();

        public BaseTool[] ToolShapes => Shapes.OfType<BaseTool>().ToArray();

        private BaseShape currentHoverShape;

        public BaseShape CurrentHoverShape
        {
            get
            {
                return currentHoverShape;
            }
            private set
            {
                if (currentHoverShape != null)
                {
                    if (PreviousHoverRectangle == Rectangle.Empty || PreviousHoverRectangle != currentHoverShape.Rectangle)
                    {
                        PreviousHoverRectangle = currentHoverShape.Rectangle;
                    }
                }
                else
                {
                    PreviousHoverRectangle = Rectangle.Empty;
                }

                currentHoverShape = value;
            }
        }

        public Rectangle PreviousHoverRectangle { get; private set; }

        public bool IsCurrentHoverShapeValid => CurrentHoverShape != null && CurrentHoverShape.IsValidShape;

        public bool IsCurrentShapeTypeRegion => IsShapeTypeRegion(CurrentTool);
        public int StartingStepNumber { get; set; } = 1;

        public bool IsCreating { get; set; }

        private bool isMoving;

        public bool IsMoving
        {
            get
            {
                return isMoving;
            }
            set
            {
                if (isMoving != value)
                {
                    isMoving = value;

                    if (isMoving)
                    {
                        Form.SetHandCursor(true);
                    }
                    else
                    {
                        Form.SetDefaultCursor();
                    }
                }
            }
        }

        private bool isPanning;

        public bool IsPanning
        {
            get
            {
                return isPanning;
            }
            set
            {
                if (isPanning != value)
                {
                    isPanning = value;

                    if (isPanning)
                    {
                        Form.SetHandCursor(true);
                    }
                    else
                    {
                        Form.SetDefaultCursor();
                    }
                }
            }
        }

        public bool IsResizing { get; set; }
        // Is holding Ctrl?
        public bool IsCtrlModifier { get; private set; }
        public bool IsCornerMoving { get; private set; }
        // Is holding Shift?
        public bool IsProportionalResizing { get; private set; }
        // Is holding Alt?
        public bool IsSnapResizing { get; private set; }
        public bool IsRenderingOutput { get; private set; }
        public Point RenderOffset { get; private set; }
        public bool IsImageModified { get; internal set; }

        public InputManager InputManager { get; private set; } = new InputManager();
        public ConcurrentBag<WindowInfo> Windows { get; set; }
        public bool WindowCaptureMode { get; set; }
        public bool IncludeControls { get; set; }

        public RegionCaptureOptions Options { get; private set; }

        public AnnotationOptions AnnotationOptions => Options.AnnotationOptions;

        internal List<ImageEditorControl> DrawableObjects { get; private set; }
        internal ResizeNode[] ResizeNodes { get; private set; }

        private bool nodesVisible;

        public bool NodesVisible
        {
            get
            {
                return nodesVisible;
            }
            set
            {
                nodesVisible = value;

                if (!nodesVisible)
                {
                    foreach (ResizeNode node in ResizeNodes)
                    {
                        node.Visible = false;
                    }
                }
                else
                {
                    BaseShape shape = CurrentShape;

                    if (shape != null)
                    {
                        shape.OnNodePositionUpdate();
                        shape.OnNodeVisible();
                    }
                }
            }
        }

        public bool IsCursorOnObject => DrawableObjects.Any(x => x.HandleMouseInput && x.IsCursorHover);

        public event Action<BaseShape> CurrentShapeChanged;
        public event Action<ShapeType> CurrentShapeTypeChanged;
        public event Action<BaseShape> ShapeCreated;
        public event Action ImageModified;

        internal RegionCaptureForm Form { get; private set; }

        private bool isLeftPressed, isRightPressed, isUpPressed, isDownPressed;

        public ShapeManager(RegionCaptureForm form)
        {
            Form = form;
            Options = form.Options;

            DrawableObjects = new List<ImageEditorControl>();
            ResizeNodes = new ResizeNode[9];

            for (int i = 0; i < ResizeNodes.Length; i++)
            {
                ResizeNode node = new ResizeNode();
                node.SetCustomNode(form.CustomNodeImage);
                DrawableObjects.Add(node);
                ResizeNodes[i] = node;
            }

            ResizeNodes[(int)NodePosition.BottomRight].Order = 10;

            form.Shown += form_Shown;
            form.LostFocus += form_LostFocus;
            form.MouseDown += form_MouseDown;
            form.MouseUp += form_MouseUp;
            form.MouseDoubleClick += form_MouseDoubleClick;
            form.MouseWheel += form_MouseWheel;
            form.KeyDown += form_KeyDown;
            form.KeyUp += form_KeyUp;

            CurrentShape = null;

            if (form.Mode == RegionCaptureMode.Annotation)
            {
                CurrentTool = Options.LastRegionTool;
            }
            else if (form.IsEditorMode)
            {
                if (Equals(form.Mode, RegionCaptureMode.WhiteBoard))
                    CurrentTool = ShapeType.画笔;
                else
                    CurrentTool = Options.LastRegionTool;
            }
            else
            {
                CurrentTool = ShapeType.矩形区域;
            }

            foreach (ImageEditorControl control in DrawableObjects)
            {
                control.MouseDown += (sender, e) => Form.SetHandCursor(true);
                control.MouseUp += (sender, e) =>
                {
                    if (control.IsCursorHover)
                    {
                        Form.SetHandCursor(false);
                    }
                    else
                    {
                        Form.SetDefaultCursor();
                    }
                };
                control.MouseEnter += () => Form.SetHandCursor(false);
                control.MouseLeave += () => Form.SetDefaultCursor();
            }
        }

        /// <summary>
        /// 用于加载编辑截图之前的选择窗口元素
        /// </summary>
        public Rectangle LastAllScreenRectangle { get; set; }

        private void form_Shown(object sender, EventArgs e)
        {
            if (Form.IsAnnotationMode || Form.IsEditorMode)
            {
                CreateToolbar();
                if (Form.IsEditorMode)
                {
                    menuForm.Shown += LoadEditImageWindowsRectangle;
                }
            }
        }

        private void LoadEditImageWindowsRectangle(object sender, EventArgs e)
        {
            if (!LastAllScreenRectangle.IsEmpty && Form.ShapeManager.Windows?.Count > 0)
            {
                //转换成当前屏幕的坐标区域
                var oldScreenRectangle = Screen.FromRectangle(LastAllScreenRectangle).Bounds;
                var lastCurrentScreenRectangle = new Rectangle(LastAllScreenRectangle.X - oldScreenRectangle.X, LastAllScreenRectangle.Y - oldScreenRectangle.Y, LastAllScreenRectangle.Width, LastAllScreenRectangle.Height);

                //计算当前屏幕内的偏移量
                var _oldOffSet = new Point(Form.CanvasRectangle.X - lastCurrentScreenRectangle.X, Form.CanvasRectangle.Y - lastCurrentScreenRectangle.Y);

                foreach (var window in Form.ShapeManager.Windows)
                {
                    window.OffSet(LastAllScreenRectangle, _oldOffSet);
                    window.Rectangle = new Rectangle(window.Rectangle.X - oldScreenRectangle.X, window.Rectangle.Y - oldScreenRectangle.Y, window.Rectangle.Width, window.Rectangle.Height);
                }
                Form.ShapeManager.Windows = new ConcurrentBag<WindowInfo>(Form.ShapeManager.Windows.Where(p => p.Rectangle.IsValid()));
            }
            if (Equals(Form.Mode, RegionCaptureMode.Editor))
                CurrentTool = ShapeType.矩形;
        }

        private void form_LostFocus(object sender, EventArgs e)
        {
            ResetModifiers();
        }

        private void form_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (!IsCreating)
                {
                    StartRegionSelection();
                }
            }
            else if (e.Button == MouseButtons.Middle)
            {
                if (Form.IsEditorMode)
                {
                    StartPanning();
                }
            }
        }

        private void form_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (IsMoving || IsCreating)
                {
                    EndRegionSelection();
                }
            }
            else if (e.Button == MouseButtons.Right)
            {
                if (IsCreating)
                {
                    DeleteCurrentShape();
                    EndRegionSelection();
                }
                else if (IsShapeIntersect())
                {
                    DeleteIntersectShape();
                }
                else
                {
                    Form.CloseWindow();
                }
            }
            else if (e.Button == MouseButtons.Middle)
            {
                if (Form.IsEditorMode)
                {
                    EndPanning();
                }
            }
        }

        private void form_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (IsCurrentShapeTypeRegion && ValidRegions.Length > 0)
                {
                    Form.UpdateRegionPath();
                    Form.CloseWindow(RegionResult.Region);
                }
                else if (CurrentShape != null && !IsCreating)
                {
                    CurrentShape.OnDoubleClicked();
                }
            }
        }

        private void form_MouseWheel(object sender, MouseEventArgs e)
        {
            if (Form.Mode == RegionCaptureMode.ScreenColorPicker)
                return;
            var isMagnifier = Form.Mode == RegionCaptureMode.Magnifier;
            if (e.Delta > 0)
            {
                if (!isMagnifier && Equals(CommonSetting.滚动鼠标, ScrollingWhenCapture.切换区域.ToString()))
                {
                    FindSelectedWindow(true);
                }
                else
                {
                    if (Options.ShowMagnifier)
                    {
                        if (isMagnifier)
                            Options.MagnifierPixelSize = Math.Min(Options.MagnifierPixelSize + 1, RegionCaptureOptions.MagnifierPixelSizeMaximum);
                        else
                            Options.MagnifierPixelCount = Math.Min(Options.MagnifierPixelCount + 2, RegionCaptureOptions.MagnifierPixelCountMaximum);
                    }
                    else
                    {
                        Options.ShowMagnifier = true;
                    }
                }
            }
            else if (e.Delta < 0)
            {
                if (!isMagnifier && Equals(CommonSetting.滚动鼠标, ScrollingWhenCapture.切换区域.ToString()))
                {
                    FindSelectedWindow(false);
                }
                else
                {
                    if (isMagnifier)
                        Options.MagnifierPixelSize = Math.Max(Options.MagnifierPixelSize - 1, RegionCaptureOptions.MagnifierPixelSizeMinimum);
                    else
                    {
                        int magnifierPixelCount = Options.MagnifierPixelCount - 2;
                        if (magnifierPixelCount < RegionCaptureOptions.MagnifierPixelCountMinimum)
                        {
                            Options.ShowMagnifier = false;
                        }
                        Options.MagnifierPixelCount = Math.Max(magnifierPixelCount, RegionCaptureOptions.MagnifierPixelCountMinimum);
                    }
                }
            }
        }

        public bool HandleEscape()
        {
            // the escape key handling has 3 stages:
            // 1. initiate exit if region selection is active
            // 2. if a shape is selected, unselect it
            // 3. switch to the select tool if a any other tool is active
            if (IsShapeTypeRegion(CurrentTool))
            {
                return false;
            }

            if (CurrentShape != null)
            {
                ClearTools();
                DeselectCurrentShape();
            }

            if (CurrentTool != ShapeType.选择并移动)
            {
                CurrentTool = ShapeType.选择并移动;
                return true;
            }

            return false;
        }

        private void form_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.ControlKey:
                    if (!IsCtrlModifier && !IsCornerMoving)
                    {
                        if (IsCreating || IsResizing)
                        {
                            IsCornerMoving = true;
                        }
                        else
                        {
                            IsCtrlModifier = true;
                        }
                    }
                    break;
                case Keys.ShiftKey:
                    IsProportionalResizing = true;
                    break;
                case Keys.Menu:
                    IsSnapResizing = true;
                    break;
                case Keys.Left:
                    isLeftPressed = true;
                    break;
                case Keys.Right:
                    isRightPressed = true;
                    break;
                case Keys.Up:
                    isUpPressed = true;
                    break;
                case Keys.Down:
                    isDownPressed = true;
                    break;
            }

            switch (e.KeyData)
            {
                case Keys.Insert:
                    if (IsCreating)
                    {
                        EndRegionSelection();
                    }
                    else
                    {
                        StartRegionSelection();
                    }
                    break;
                case Keys.Delete:
                    DeleteCurrentShape();

                    if (IsCreating)
                    {
                        EndRegionSelection();
                    }
                    break;
                case Keys.Shift | Keys.Delete:
                    DeleteAllShapes();
                    break;
            }

            if (!IsCreating)
            {
                if (Form.Mode == RegionCaptureMode.Annotation)
                {
                    switch (e.KeyData)
                    {
                        case Keys.Tab:
                            SwapShapeType();
                            break;
                        case Keys.NumPad0:
                            CurrentTool = ShapeType.矩形区域;
                            break;
                    }
                }

                if (Form.IsEditorMode)
                {
                    switch (e.KeyData)
                    {
                        //case Keys.C:
                        //    CurrentTool = ShapeType.裁剪;
                        //    break;
                        case Keys.Control | Keys.S:
                        case Keys.Control | Keys.Shift | Keys.S:
                            Form.OnSaveImageAsRequested();
                            break;
                        case Keys.Control | Keys.Shift | Keys.C:
                            Form.OnCopyImageRequested();
                            break;
                    }
                }
            }

            int speed;

            if (e.Shift)
            {
                speed = RegionCaptureOptions.MoveSpeedMaximum;
            }
            else
            {
                speed = RegionCaptureOptions.MoveSpeedMinimum;
            }

            int x = 0;

            if (isLeftPressed)
            {
                x -= speed;
            }

            if (isRightPressed)
            {
                x += speed;
            }

            int y = 0;

            if (isUpPressed)
            {
                y -= speed;
            }

            if (isDownPressed)
            {
                y += speed;
            }

            if (x != 0 || y != 0)
            {
                BaseShape shape = CurrentShape;

                if (shape == null || IsCreating)
                {
                    Cursor.Position = Cursor.Position.Add(x, y);
                }
                else if (e.Control)
                {
                    shape.OnResizing();
                    shape.Resize(x, y, true);
                }
                else if (e.Alt)
                {
                    shape.OnResizing();
                    shape.Resize(x, y, false);
                }
                else
                {
                    shape.OnMoving();
                    shape.Move(x, y);
                }
            }
        }

        private void form_KeyUp(object sender, KeyEventArgs e)
        {
            bool wasMoving = isLeftPressed || isRightPressed || isUpPressed || isDownPressed;

            switch (e.KeyCode)
            {
                case Keys.ControlKey:
                    IsCtrlModifier = false;
                    IsCornerMoving = false;
                    break;
                case Keys.ShiftKey:
                    IsProportionalResizing = false;
                    break;
                case Keys.Menu:
                    IsSnapResizing = false;
                    break;
                case Keys.Left:
                    isLeftPressed = false;
                    break;
                case Keys.Right:
                    isRightPressed = false;
                    break;
                case Keys.Up:
                    isUpPressed = false;
                    break;
                case Keys.Down:
                    isDownPressed = false;
                    break;
            }

            if (!IsCreating && !IsMoving && wasMoving)
            {
                bool isMoving = isLeftPressed || isRightPressed || isUpPressed || isDownPressed;

                if (!isMoving)
                {
                    ShapeMoved();
                }
            }
        }

        private void ShapeMoved()
        {
            if (!IsCreating)
            {
                BaseShape shape = CurrentShape;

                if (shape != null)
                {
                    shape.OnMoved();
                }
            }
        }

        public void Update()
        {
            OrderStepShapes();

            BaseShape shape = CurrentShape;

            if (shape != null)
            {
                shape.OnUpdate();
            }

            UpdateCurrentHoverShape();

            UpdateNodes();
        }

        public void StartRegionSelection()
        {
            if (IsCursorOnObject)
            {
                return;
            }

            InputManager.Update(Form); // If it's a touch event we don't have the correct point yet, so refresh it now

            BaseShape shape = GetIntersectShape();

            if (shape != null && shape.IsSelectable) // Select shape
            {
                DeselectCurrentShape();
                IsMoving = true;
                shape.OnMoving();
                CurrentShape = shape;
                SelectCurrentShape();
            }
            else if (shape == null && CurrentTool == ShapeType.选择并移动)
            {
                ClearTools();
                DeselectCurrentShape();
            }
            else if (!IsCreating && CurrentTool != ShapeType.选择并移动) // Create new shape
            {
                ClearTools();
                DeselectCurrentShape();

                shape = AddShape();
                shape.OnCreating();
            }
        }

        public void EndRegionSelection()
        {
            bool wasCreating = IsCreating;
            bool wasMoving = IsMoving;

            IsCreating = false;
            IsMoving = false;

            BaseShape shape = CurrentShape;

            if (shape != null)
            {
                if (!shape.IsValidShape)
                {
                    shape.Rectangle = Rectangle.Empty;

                    UpdateCurrentHoverShape();

                    if (IsCurrentHoverShapeValid)
                    {
                        shape.Rectangle = CurrentHoverShape.Rectangle;
                    }
                    else
                    {
                        DeleteCurrentShape();
                        shape = null;
                    }
                }

                if (shape != null)
                {
                    if (Options.QuickCrop && IsCurrentShapeTypeRegion)
                    {
                        Form.UpdateRegionPath();
                        Form.CloseWindow(RegionResult.Region);
                    }
                    else
                    {
                        if (wasCreating)
                        {
                            shape.OnCreated();

                            OnShapeCreated(shape);

                            SelectCurrentShape();

                            if (Options.SwitchToSelectionToolAfterDrawing && shape.IsHandledBySelectTool)
                            {
                                CurrentTool = ShapeType.选择并移动;
                            }
                        }
                        else if (wasMoving)
                        {
                            shape.OnMoved();
                            SelectCurrentShape();
                        }
                    }
                }
            }
        }

        private void StartPanning()
        {
            IsPanning = true;
            Form.PanningStrech = new Point(0, 0);
        }

        private void EndPanning()
        {
            IsPanning = false;
        }

        internal void UpdateObjects()
        {
            ImageEditorControl[] objects = DrawableObjects.OrderByDescending(x => x.Order).ToArray();

            Point position = InputManager.ClientMousePosition;

            if (objects.All(x => !x.IsDragging))
            {
                for (int i = 0; i < objects.Length; i++)
                {
                    ImageEditorControl obj = objects[i];

                    if (obj.Visible)
                    {
                        obj.IsCursorHover = obj.Rectangle.Contains(position);

                        if (obj.IsCursorHover)
                        {
                            if (InputManager.IsMousePressed(MouseButtons.Left))
                            {
                                obj.OnMouseDown(position);
                            }

                            for (int j = i + 1; j < objects.Length; j++)
                            {
                                objects[j].IsCursorHover = false;
                            }

                            break;
                        }
                    }
                    else
                    {
                        obj.IsCursorHover = false;
                    }
                }
            }
            else
            {
                if (InputManager.IsMouseReleased(MouseButtons.Left))
                {
                    foreach (ImageEditorControl obj in objects)
                    {
                        if (obj.IsDragging)
                        {
                            obj.OnMouseUp(position);
                        }
                    }
                }
            }
        }

        internal void DrawObjects(Graphics g)
        {
            foreach (ImageEditorControl obj in DrawableObjects)
            {
                if (obj.Visible)
                {
                    obj.OnDraw(g);
                }
            }
        }

        private BaseShape AddShape()
        {
            BaseShape shape = CreateShape();
            AddShape(shape);
            return shape;
        }

        private void AddShape(BaseShape shape)
        {
            Shapes.Add(shape);
            CurrentShape = shape;

            if (shape.ShapeCategory == ShapeCategory.Drawing || shape.ShapeCategory == ShapeCategory.Effect)
            {
                OnImageModified();
            }
        }

        private BaseShape CreateShape()
        {
            return CreateShape(CurrentTool);
        }

        private BaseShape CreateShape(ShapeType shapeType)
        {
            BaseShape shape;

            switch (shapeType)
            {
                default:
                case ShapeType.矩形区域:
                    shape = new RectangleRegionShape();
                    break;
                case ShapeType.圆形区域:
                    shape = new EllipseRegionShape();
                    break;
                case ShapeType.自由截图:
                    shape = new FreehandRegionShape();
                    break;
                case ShapeType.矩形:
                    shape = new RectangleDrawingShape();
                    break;
                case ShapeType.圆形:
                    shape = new EllipseDrawingShape();
                    break;
                case ShapeType.画笔:
                    shape = new FreehandDrawingShape();
                    break;
                case ShapeType.直线:
                    shape = new LineDrawingShape();
                    break;
                case ShapeType.箭头:
                    shape = new ArrowDrawingShape();
                    break;
                case ShapeType.文字:
                    shape = new TextOutlineDrawingShape();
                    break;
                case ShapeType.文字描边:
                    shape = new TextDrawingShape();
                    break;
                case ShapeType.气泡:
                    shape = new SpeechBalloonDrawingShape();
                    break;
                case ShapeType.序号:
                    shape = new StepDrawingShape();
                    break;
                case ShapeType.放大镜:
                    shape = new MagnifyDrawingShape();
                    break;
                case ShapeType.橡皮擦:
                    shape = new SmartEraserDrawingShape();
                    break;
                case ShapeType.马赛克:
                    shape = new PixelateEffectShape();
                    break;
                case ShapeType.高亮:
                    shape = new HighlightEffectShape();
                    break;
                    //case ShapeType.裁剪:
                    //    shape = new CropTool();
                    //    break;
            }

            shape.Manager = this;

            shape.OnConfigLoad();

            return shape;
        }

        private void UpdateCurrentShape()
        {
            BaseShape shape = CurrentShape;

            if (shape != null)
            {
                shape.OnConfigLoad();
                shape.OnMoved();
            }

            Form.Resume();
        }

        private void SwapShapeType()
        {
            if (Form.Mode == RegionCaptureMode.Annotation)
            {
                if (IsCurrentShapeTypeRegion)
                {
                    CurrentTool = Options.LastAnnotationTool;
                }
                else
                {
                    CurrentTool = Options.LastRegionTool;
                }
            }
        }

        public Point SnapPosition(Point posOnClick, Point posCurrent)
        {
            Size currentSize = ImageHelp.CreateRectangle(posOnClick, posCurrent).Size;
            var vector = new Vector(currentSize.Width, currentSize.Height);

            SnapSize snapSize = (from size in Options.SnapSizes
                                 let distance = MathHelpers.Distance(vector, new Vector(size.Width, size.Height))
                                 where distance > 0 && distance < RegionCaptureOptions.SnapDistance
                                 orderby distance
                                 select size).FirstOrDefault();

            if (snapSize != null)
            {
                Point posNew = ImageHelp.CalculateNewPosition(posOnClick, posCurrent, snapSize);

                Rectangle newRect = ImageHelp.CreateRectangle(posOnClick, posNew);

                if (Form.ClientArea.Contains(newRect))
                {
                    return posNew;
                }
            }

            return posCurrent;
        }

        private void UpdateCurrentHoverShape()
        {
            CurrentHoverShape = CheckHover();
        }

        private BaseShape CheckHover()
        {
            if (!IsCursorOnObject && !IsCreating && !IsMoving && !IsResizing)
            {
                BaseShape shape = GetIntersectShape();

                if (shape != null && shape.IsValidShape)
                {
                    return shape;
                }

                switch (CurrentTool)
                {
                    case ShapeType.自由截图:
                    case ShapeType.画笔:
                    case ShapeType.直线:
                    case ShapeType.箭头:
                    case ShapeType.文字:
                    case ShapeType.文字描边:
                    case ShapeType.气泡:
                    case ShapeType.序号:
                    case ShapeType.选择并移动:
                        return null;
                }

                FindSelectedWindow();

                if (nowSelectedWindow != null && !nowSelectedWindow.Rectangle.IsEmpty)
                {
                    Rectangle hoverArea = NativeMethods.ScreenToClient(nowSelectedWindow.Rectangle);

                    BaseShape rectangleRegionShape = CreateShape(ShapeType.矩形区域);
                    rectangleRegionShape.Rectangle = Rectangle.Intersect(Form.ClientArea, hoverArea);
                    return rectangleRegionShape;
                }
            }

            return null;
        }

        private WindowInfo nowSelectedWindow = null;
        public WindowInfo NowSelectedWindow
        {
            get
            {
                if (Equals(nowSelectedWindow, null))
                {
                    nowSelectedWindow = FindSelectedWindow();
                }
                return nowSelectedWindow;
            }
        }

        private Point lastMousePoint = new Point();
        private WindowInfo FindSelectedWindow(bool? zoom = null)
        {
            var mousePos = Form.IsEditorMode ? Form.PointToClient(InputManager.MousePosition) : InputManager.MousePosition;

            if (!Equals(lastMousePoint, mousePos))
            {
                nowSelectedWindow = null;
            }
            lastMousePoint = mousePos;

            if (Form.IsEditorMode && !Form.ClientRectangle.Contains(mousePos))
            {
                return null;
            }

            // 🔥 关键：只考虑包含鼠标位置的控件
            var candidatesAtMouse = Windows?.Where(p => p.Rectangle.Contains(mousePos));
            if (!IsSmallControlModel)
                candidatesAtMouse = candidatesAtMouse?.Where(p => !p.IsSmallControl);

            var sortedCandidates = candidatesAtMouse?
                .OrderByDescending(p => p.ZIndex)
                .ThenByDescending(p => p.GetLevel(Windows)) // 优先选择层级更深的控件
                .ThenBy(p => p.Rectangle.Width * p.Rectangle.Height)
                .ToList();

            if (sortedCandidates == null || !sortedCandidates.Any())
            {
                return null;
            }

            // 初始选择逻辑
            if (nowSelectedWindow == null || !sortedCandidates.Contains(nowSelectedWindow))
            {
                nowSelectedWindow = sortedCandidates.First();
                return nowSelectedWindow;
            }

            // 滚轮导航
            if (zoom.HasValue)
            {
                nowSelectedWindow = HandleHierarchicalNavigation(zoom.Value, sortedCandidates);
            }

            return nowSelectedWindow;
        }

        private WindowInfo HandleHierarchicalNavigation(bool zoomIn, List<WindowInfo> candidatesAtMouse)
        {
            if (nowSelectedWindow == null) return candidatesAtMouse.FirstOrDefault();

            if (zoomIn) // 向上滚动：向父级导航
            {
                return NavigateToParent(candidatesAtMouse);
            }
            else // 向下滚动：向子级导航
            {
                return NavigateToChild(candidatesAtMouse);
            }
        }

        private WindowInfo NavigateToParent(List<WindowInfo> candidatesAtMouse)
        {
            // 1. 查找直接父级
            var directParent = nowSelectedWindow.GetDirectParent(Windows);

            // 2. 如果直接父级在鼠标位置候选列表中，选择它
            if (directParent != null && candidatesAtMouse.Contains(directParent))
            {
                return directParent;
            }

            // 3. 继续向上查找，直到找到在鼠标位置的父级
            var current = directParent;
            while (current != null)
            {
                if (candidatesAtMouse.Contains(current))
                {
                    return current;
                }
                current = current.GetDirectParent(Windows);
            }

            // 4. 没有找到父级，在同层级中循环切换
            return NavigateInSameLevel(candidatesAtMouse, true);
        }

        private WindowInfo NavigateToChild(List<WindowInfo> candidatesAtMouse)
        {
            // 1. 获取当前控件的直接子控件
            var directChildren = nowSelectedWindow.GetDirectChildren(Windows);

            // 2. 找到在鼠标位置的子控件
            var childrenAtMouse = directChildren.Where(child => candidatesAtMouse.Contains(child)).ToList();

            if (childrenAtMouse.Any())
            {
                // 选择面积最小的子控件（最具体的控件）
                return childrenAtMouse.OrderBy(c => c.Rectangle.Width * c.Rectangle.Height).First();
            }

            // 3. 递归查找更深层的子控件
            foreach (var child in directChildren)
            {
                var deeperChild = FindDeepestChildAtMouse(child, candidatesAtMouse);
                if (deeperChild != null) return deeperChild;
            }

            // 4. 没有找到子级，在同层级中循环切换
            return NavigateInSameLevel(candidatesAtMouse, false);
        }

        private WindowInfo FindDeepestChildAtMouse(WindowInfo parent, List<WindowInfo> candidatesAtMouse)
        {
            var children = parent.GetDirectChildren(Windows);
            var childrenAtMouse = children.Where(child => candidatesAtMouse.Contains(child)).ToList();

            if (childrenAtMouse.Any())
            {
                // 递归查找更深层的子控件
                foreach (var child in childrenAtMouse)
                {
                    var deeperChild = FindDeepestChildAtMouse(child, candidatesAtMouse);
                    if (deeperChild != null) return deeperChild;
                }

                // 返回当前层级面积最小的控件
                return childrenAtMouse.OrderBy(c => c.Rectangle.Width * c.Rectangle.Height).First();
            }

            return null;
        }

        private WindowInfo NavigateInSameLevel(List<WindowInfo> candidatesAtMouse, bool forward)
        {
            var currentLevel = nowSelectedWindow.GetLevel(Windows);
            var sameLevelWindows = candidatesAtMouse
                .Where(w => w.GetLevel(Windows) == currentLevel)
                .OrderBy(w => w.Rectangle.Width * w.Rectangle.Height)
                .ToList();

            if (sameLevelWindows.Count <= 1) return nowSelectedWindow;

            var currentIndex = sameLevelWindows.IndexOf(nowSelectedWindow);
            if (currentIndex < 0) return nowSelectedWindow;

            if (forward)
            {
                return currentIndex < sameLevelWindows.Count - 1
                    ? sameLevelWindows[currentIndex + 1]
                    : sameLevelWindows[0]; // 循环到第一个
            }
            else
            {
                return currentIndex > 0
                    ? sameLevelWindows[currentIndex - 1]
                    : sameLevelWindows[sameLevelWindows.Count - 1]; // 循环到最后一个
            }
        }

        public Bitmap RenderOutputImage(Bitmap bmp)
        {
            return RenderOutputImage(bmp, Point.Empty);
        }

        public Bitmap RenderOutputImage(Bitmap bmp, Point offset)
        {
            Bitmap bmpOutput = (Bitmap)bmp.Clone();

            if (DrawingShapes.Length > 0 || EffectShapes.Length > 0)
            {
                IsRenderingOutput = true;
                RenderOffset = offset;

                MoveAll(-offset.X, -offset.Y);

                using (Graphics g = Graphics.FromImage(bmpOutput))
                {
                    foreach (BaseEffectShape shape in EffectShapes)
                    {
                        if (shape != null)
                        {
                            shape.OnDrawFinal(g, bmpOutput);
                        }
                    }

                    foreach (BaseDrawingShape shape in DrawingShapes)
                    {
                        if (shape != null)
                        {
                            shape.OnDraw(g);
                        }
                    }
                }

                MoveAll(offset);

                RenderOffset = Point.Empty;
                IsRenderingOutput = false;
            }

            return bmpOutput;
        }

        private void SelectShape(BaseShape shape)
        {
            if (shape != null)
            {
                shape.ShowNodes();

                if (Options.SwitchToDrawingToolAfterSelection)
                {
                    CurrentTool = shape.ShapeType;
                }
            }
        }

        private void SelectCurrentShape()
        {
            SelectShape(CurrentShape);
        }

        private void DeselectShape(BaseShape shape)
        {
            if (shape == CurrentShape)
            {
                CurrentShape = null;
                NodesVisible = false;
            }
        }

        private void DeselectCurrentShape()
        {
            DeselectShape(CurrentShape);
        }

        public void DeleteShape(BaseShape shape)
        {
            if (shape != null)
            {
                shape.Dispose();
                Shapes.Remove(shape);
                DeselectShape(shape);

                if (shape.ShapeCategory == ShapeCategory.Drawing || shape.ShapeCategory == ShapeCategory.Effect)
                {
                    OnImageModified();
                }

                UpdateMenu();
            }
        }

        private void DeleteCurrentShape()
        {
            DeleteShape(CurrentShape);
        }

        private void DeleteIntersectShape()
        {
            DeleteShape(GetIntersectShape());
        }

        private void DeleteAllShapes()
        {
            if (Shapes.Count > 0)
            {
                foreach (BaseShape shape in Shapes)
                {
                    shape.Dispose();
                }

                Shapes.Clear();
                DeselectCurrentShape();
                OnImageModified();
            }
        }

        private void ResetModifiers()
        {
            IsCtrlModifier = IsCornerMoving = IsProportionalResizing = IsSnapResizing = false;
        }

        private void ClearTools()
        {
            foreach (BaseTool tool in ToolShapes)
            {
                tool.Dispose();
                Shapes.Remove(tool);
            }
        }

        public BaseShape GetIntersectShape()
        {
            return GetIntersectShape(InputManager.ClientMousePosition);
        }

        public BaseShape GetIntersectShape(Point position)
        {
            if (!IsCtrlModifier)
            {
                var shape = Shapes.FirstOrDefault(p => CurrentTool == p.ShapeType && p.IsSelectable && p.Intersects(position)) ??
                            Shapes.FirstOrDefault(p => p.IsSelectable && p.Intersects(position));
                return shape;
            }

            return null;
        }

        public bool IsShapeIntersect()
        {
            return GetIntersectShape() != null;
        }

        public void UndoShape()
        {
            if (Shapes.Count > 0)
            {
                DeleteShape(Shapes[Shapes.Count - 1]);
            }
        }

        public void MoveAll(int x, int y)
        {
            if (x != 0 || y != 0)
            {
                foreach (BaseShape shape in Shapes)
                {
                    shape.Move(x, y);
                }
            }
        }

        public void MoveAll(Point offset)
        {
            MoveAll(offset.X, offset.Y);
        }

        private bool IsShapeTypeRegion(ShapeType shapeType)
        {
            switch (shapeType)
            {
                case ShapeType.矩形区域:
                case ShapeType.圆形区域:
                case ShapeType.自由截图:
                case ShapeType.截图问题反馈:
                    return true;
            }

            return false;
        }

        private void UpdateNodes()
        {
            BaseShape shape = CurrentShape;

            if (shape != null && NodesVisible)
            {
                if (InputManager.IsMouseDown(MouseButtons.Left))
                {
                    shape.OnNodeUpdate();
                }
                else if (IsResizing)
                {
                    IsResizing = false;

                    shape.OnResized();
                }

                shape.OnNodePositionUpdate();
            }
        }

        public void OrderStepShapes()
        {
            int i = StartingStepNumber;

            foreach (StepDrawingShape shape in Shapes.OfType<StepDrawingShape>())
            {
                shape.Number = i++;
            }
        }

        public Bitmap CropImage(Rectangle rect, bool onlyIfSizeDifferent = false)
        {
            rect = NativeMethods.ScreenToClient(rect);
            Point offset = NativeMethods.ScreenToClient(Form.CanvasRectangle.Location);
            rect.X -= offset.X;
            rect.Y -= offset.Y;
            rect.Intersect(new Rectangle(0, 0, Form.Canvas.Width, Form.Canvas.Height));

            if (rect.IsValid() && (!onlyIfSizeDifferent || rect.Size != Form.Canvas.Size))
            {
                return ImageProcessHelper.CropBitmap(Form.Canvas, rect);
            }

            return null;
        }

        public Color GetColor(Bitmap bmp, Point pos)
        {
            if (bmp != null)
            {
                Point position = NativeMethods.ScreenToClient(pos);
                Point offset = NativeMethods.ScreenToClient(Form.CanvasRectangle.Location);
                position.X -= offset.X;
                position.Y -= offset.Y;

                if (position.X.IsBetween(0, bmp.Width - 1) && position.Y.IsBetween(0, bmp.Height - 1))
                {
                    return bmp.GetPixel(position.X, position.Y);
                }
            }

            return Color.Empty;
        }

        public Color GetCurrentColor(Bitmap bmp)
        {
            return GetColor(bmp, InputManager.ClientMousePosition);
        }

        public Color GetCurrentColor()
        {
            return GetCurrentColor(Form.Canvas);
        }

        private bool PickColor(Color currentColor, out Color newColor)
        {
            return ColorPickerForm.PickColor(currentColor, out newColor, true);
            //newColor = CommonMethod.CatchImageColor(Form.IsEditorMode ? null : new Bitmap(Form.Canvas), currentColor);
            //return true;
        }

        private void OnCurrentShapeChanged(BaseShape shape)
        {
            CurrentShapeChanged?.Invoke(shape);
        }

        private void OnCurrentShapeTypeChanged(ShapeType shapeType)
        {
            CurrentShapeTypeChanged?.Invoke(shapeType);
        }

        private void OnShapeCreated(BaseShape shape)
        {
            ShapeCreated?.Invoke(shape);
        }

        private void OnImageModified()
        {
            IsImageModified = true;

            ImageModified?.Invoke();
        }

        public void Dispose()
        {
            DeleteAllShapes();
        }
    }
}